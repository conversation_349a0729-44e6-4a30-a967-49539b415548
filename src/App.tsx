import React, { useEffect, useState, createContext, Suspense, useMemo, ComponentType } from 'react';
import '@fontsource/open-sans/500.css';
import '@nexusui/theme/fonts';
import frLocale from 'date-fns/locale/fr';
import itLocale from 'date-fns/locale/it';
import deLocale from 'date-fns/locale/de';
import enUSLocale from 'date-fns/locale/en-US';
import enGBLocale from 'date-fns/locale/en-GB';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import CssBaseline from '@mui/material/CssBaseline';
import { Box, Fade } from '@mui/material';
import './App.css';
import { LanguageType, useThemeWithLocale, nexusThemeConfig } from '@nexusui/theme';
import { ThemeProvider } from '@mui/material/styles';
import type {} from '@mui/material/themeCssVarsAugmentation';
import { Outlet, useSearchParams } from 'react-router-dom';
import HexNavigationBar from './components/HexAppBar/HexNavigationBar';
import { useAuth } from 'react-oidc-context';
// Passing the data required to register the flag schema
import { AppInsightsContext } from '@microsoft/applicationinsights-react-js';
import { reactPlugin } from './AppInsights';
import styled from '@emotion/styled';
import { LoggedUserContext, UserRoles } from './context/AdminContext';
import * as microsoftTeams from '@microsoft/teams-js';
import { inTeams } from './components/IntegrateToTeams/teams-js-hooks/utils/inTeams';
import { getNoAuthRoutes } from './routes/NoAuthRoutes';
import { InformationWithProgressBackDrop } from './components/BackDrops/InformationWithProgressBackDrop';
import HexWaterMark from './components/HexWaterMark/HexWaterMark';
import jwtDecode from 'jwt-decode';
import { sdcPrivilegeContext } from './context/SdcUserPrivilegesContext';
import { PRIVILEGE_LOCATOR, BASE_APP_PATH } from './configuration/URL.config';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from './redux';
import { withLDProvider, useLDClient, useFlags } from 'launchdarkly-react-client-sdk';
import { LD_CONFIG } from './configuration/LD.config';
import { t } from 'i18next';
import { cleanSensitiveInfo, handleErrors } from './utils/helper';
import { changeTheme, updateUser } from './redux/slices/userProfile';
import * as gridLocales from '@mui/x-data-grid/locales';
import * as dateLocales from '@mui/x-date-pickers/locales';
import LanguageSelector from './components/LanguageSelector/LanguageSelector';
import { AuthContextProvider } from '@nexusui/connected-components/extended/AuthContextProvider';
import { SnackbarProvider } from '@nexusui/components';
import { emailLocator, familyNameLocator, givenNameLocator, pictureLocator } from './configuration/AUTH.config';
import { useHexAuth } from '@nexusplatform/react';
import { LicenseInfo } from '@mui/x-license';

LicenseInfo.setLicenseKey('04ecdb341ed39c0e71479cd612ef5dcbTz0xMDM4OTMsRT0xNzY1MTkzNDYwMDAwLFM9cHJvLExNPXN1YnNjcmlwdGlvbixQVj1pbml0aWFsLEtWPTI=');

const dateFnsLocales: Record<string, any> = {
  'en-US': enUSLocale,
  'en-GB': enGBLocale,
  'fr-FR': frLocale,
  'de-DE': deLocale,
  'it-IT': itLocale
};

export const TagContext = createContext('web');
export const SwitchContext = createContext(true);
export const SignalrContext = createContext({});
// TODO: get this from Auth0

const currentUserRole = UserRoles.User;
// if (window.location.href.toString().toLowerCase().includes(UserRoles.VendorAdmin)) {
//   currentUserRole = UserRoles.VendorAdmin;
// } else if (window.location.href.toString().toLowerCase().includes(UserRoles.PartnerAdmin)) {
//   currentUserRole = UserRoles.PartnerAdmin;
// } else if (window.location.href.toString().toLowerCase().includes(UserRoles.Admin)) {
//   currentUserRole = UserRoles.Admin;
// } else {
//   currentUserRole = UserRoles.User;
// }

const triggerNativeAppEvent = (eventName: string) => {
  const logoutEvent = new CustomEvent(`nexus:${eventName}`, { detail: {} });
  window.dispatchEvent(logoutEvent);

  // Code added here to for additive manufacturing
  const externalCefEventHandler = `nexus:${eventName}` in window ? (window as { [key: string]: any })[`nexus:${eventName}`] : undefined;
  if (externalCefEventHandler) {
    externalCefEventHandler(JSON.stringify({ detail: {} }));
  }
};

export const handleLogout = (signoutRedirect: any, searchParams: URLSearchParams) => {
  let appendUrl: string = import.meta.env.VITE_APP_BASE !== 'dev' ? '/platform-landing' : '';
  if (!appendUrl.endsWith('/')) {
    appendUrl += '/';
  }
  const appParam = searchParams.get('app') ? `?app=${searchParams.get('app')}` : '';
  const filterParamPrefix = appParam ? '&' : '?';
  const filterParam = searchParams.get('filter') ? `${filterParamPrefix}filter=${searchParams.get('filter')}` : '';
  const returnToUrl = window.location.origin + appendUrl + appParam + filterParam;
  triggerNativeAppEvent('logout');
  signoutRedirect({
    post_logout_redirect_uri: returnToUrl
  });
};

/**
 * Entry point to the react application
 * @return {React.ReactNode} ReactNode
 */
function App(): React.ReactNode {
  const isInTeams = inTeams();
  const language = useSelector<RootState, LanguageType>((state) => state.userProfile.language);
  const userDetails = useSelector((state: RootState) => state.userProfile.user);
  const themeMode = useSelector<RootState, 'light' | 'dark'>((state) => state.userProfile.theme);
  const dateFnsLocale = useMemo(() => dateFnsLocales[language], [language]);
  const themeWithLocale = useThemeWithLocale(language as LanguageType, nexusThemeConfig, gridLocales[language], dateLocales[language]);
  const { signoutRedirect, isAuthenticated, isLoading } = useAuth();
  const [searchParams] = useSearchParams();
  const ldClient = useLDClient();

  const [selectedApp, setSelectedApp] = useState<string>('web');
  const [initialized, setInitialized] = useState(false);
  // project sdc view doesn't need the global navbar,so hide it.
  const showHeader =
    !getNoAuthRoutes().includes(location.pathname) &&
    !location.pathname.includes('/project/') &&
    !location.pathname.includes('/whiteboard/') &&
    !location.pathname.includes('/component/');
  const [sdcPrivileges, setSdcPrivileges] = useState<string[]>([]);
  const [viewSwitcher, setViewSwitcher] = useState(false);

  const dispatch = useDispatch();
  const { platformIntentoWidgetVisible } = useFlags();
  const { getAccessTokenSilently } = useHexAuth();

  useEffect(() => {
    if (!ldClient) return;
    if (!userDetails.id) return;
    const context = {
      kind: 'user',
      key: userDetails?.id,
      email: userDetails?.email,
      familyName: userDetails?.lastName,
      givenName: userDetails?.firstName,
      _meta: {
        privateAttributes: ['familyName', 'givenName']
      }
    };

    ldClient.identify(context);
  }, [ldClient, userDetails]);

  useEffect(() => {
    const handleBeforeUnload = () => {
      cleanSensitiveInfo(false);
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, []);

  const errorEventListener = (e: any) => {
    handleErrors(e);
  };

  useEffect(() => {
    const getPrivilegesFromToken = async () => {
      try {
        const token = await getAccessTokenSilently();
        if (token) {
          const decoded = jwtDecode<{ [key: string]: string }>(token as string);
          const p = decoded[PRIVILEGE_LOCATOR].split(' ');
          setSdcPrivileges(p.filter((e) => String(e).trim()));

          const userObject = {
            id: decoded.sub,
            email: decoded[emailLocator] ?? '',
            avatar: decoded[pictureLocator] ?? '',
            firstName: decoded[givenNameLocator] ?? '',
            lastName: decoded[familyNameLocator] ?? ''
          };
          dispatch(updateUser(userObject));
        }
      } catch (error) {
        console.error('Error fetching privileges from token:', error);
      }
    };
    if (isAuthenticated) {
      getPrivilegesFromToken();
      window.addEventListener('error', errorEventListener);
      return () => {
        window.removeEventListener('error', errorEventListener);
      };
    }
  }, [getAccessTokenSilently, isAuthenticated]);

  useEffect(() => {
    if (isAuthenticated) {
      triggerNativeAppEvent('login');
    }
  }, [isAuthenticated]);

  useEffect(() => {
    const appType = searchParams.get('app');
    if (appType && selectedApp !== appType) {
      setSelectedApp('' + appType);
    }
  }, [searchParams, selectedApp]);

  useEffect(() => {
    if (!initialized) {
      if (isInTeams) {
        microsoftTeams.app
          .initialize()
          .then(() => {
            microsoftTeams.app.notifyAppLoaded();
            microsoftTeams.app.notifySuccess();
            setInitialized(true);
          })
          .catch((error: any) => console.error(error));
        microsoftTeams.app
          .getContext()
          .then((context) => {
            dispatch(changeTheme(context.app.theme === 'default' ? 'light' : 'dark'));
          })
          .catch((error) => console.error(error));
      } else {
        setInitialized(true);
      }
    }
  }, [dispatch, initialized, isInTeams]);

  if (isLoading) {
    return <InformationWithProgressBackDrop open={true} message={t('nexusPlatform')} description={t('loadingYourExperience')} />;
  }

  if (initialized) {
    return (
      <AppInsightsContext.Provider value={reactPlugin}>
        <ThemeProvider theme={themeWithLocale} defaultMode={themeMode}>
          <CssBaseline>
            <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={dateFnsLocale}>
              <SnackbarProvider maxSnack={3} anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }} autoHideDuration={6000} TransitionComponent={Fade} hideIconVariant>
                <AuthContextProvider baseUrl={BASE_APP_PATH}>
                  <sdcPrivilegeContext.Provider value={{ sdcPrivileges }}>
                    {platformIntentoWidgetVisible && <LanguageSelector />}
                    <LoggedUserContext.Provider value={{ currentUserRole }}>
                      <HexWaterMark url={window.location.href} />
                      {isAuthenticated && showHeader && (
                        <HexNavigationBar user={userDetails ?? {}} logout={() => handleLogout(signoutRedirect, searchParams)} {...{ viewSwitcher, setViewSwitcher }} />
                      )}
                      <AppStyled className="hex-main-container alt" id="hex-main-container">
                        <section className="hex-flex-container hex-right-panel-container">
                          <TagContext.Provider value={selectedApp}>
                            <SwitchContext.Provider value={viewSwitcher}>
                              <Suspense fallback={<InformationWithProgressBackDrop open={true} message={t('nexusPlatform')} description={t('loadingYourExperience')} />}>
                                <Outlet />
                              </Suspense>
                            </SwitchContext.Provider>
                          </TagContext.Provider>
                        </section>
                      </AppStyled>
                    </LoggedUserContext.Provider>
                  </sdcPrivilegeContext.Provider>
                </AuthContextProvider>
              </SnackbarProvider>
            </LocalizationProvider>
          </CssBaseline>
        </ThemeProvider>
      </AppInsightsContext.Provider>
    );
  } else {
    return <></>;
  }
}

export default withLDProvider({
  clientSideID: LD_CONFIG.clientSideID,
  reactOptions: {
    useCamelCaseFlagKeys: true
  }
})(App as ComponentType<Record<string, never>>);

const AppStyled = styled(Box)`
  .hex-flex-container {
    display: flex;
    flex-direction: column;
  }

  .hex-loading-container {
    align-items: center;
    justify-content: center;
  }
  .hex-loading-animation {
    width: 50%;
    height: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .hex-left-menu {
    width: 3.5rem;
    height: 100%;
    background: linear-gradient(180deg, #00526f 0%, #00011b 100%);
    display: flex;
    flex-direction: column;
  }
  .hex-flex-row {
    display: flex;
  }
  .hex-left-menu .item {
    color: white;
    fill: white;
    display: flex;
    flex-direction: column;
  }
  .hex-left-menu .item.selected {
    background: white;
    color: #00011b;
    fill: #00011b;
  }

  .hex-left-menu .item:hover {
    background: white;
    color: #00011b;
    fill: #00011b;
  }
  .hex-right-panel-container {
    flex-grow: 1;
    position: relative;
    overflow-x: hidden;
  }

  .hex-title-row {
    padding: 0.75rem 0.5rem 0;
  }

  .hex-label-row {
    padding: 1rem 1rem;
    display: flex;
    flex-wrap: wrap;
    flex-direction: row;
  }
  .hex-loader {
    border: 5px solid #f3f3f3;
    border-radius: 50%;
    border-top: 5px solid #3498db;
    width: 20px;
    height: 20px;
    -webkit-animation: spin 2s linear infinite;
    animation: spin 2s linear infinite;
    margin: auto;
    left: 0;
    right: 0;
    top: 30%;
    bottom: 0;
    position: absolute;
  }

  @-webkit-keyframes spin {
    0% {
      -webkit-transform: rotate(0deg);
    }

    100% {
      -webkit-transform: rotate(360deg);
    }
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }

    100% {
      transform: rotate(360deg);
    }
    .hex-project-list-view {
      display: flex;
      flex-direction: column;
      width: 100%;
      border: 1px solid red;
      position: relative;
      top: 10px;
    }
  }
`;
