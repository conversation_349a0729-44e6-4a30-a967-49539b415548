{"name": "nexus-platform-react-app", "version": "0.0.1", "private": true, "dependencies": {"@emotion/react": "^11.7.1", "@emotion/styled": "^11.6.0", "@fluid-experimental/property-binder": "1.3.6", "@fluid-experimental/property-changeset": "1.3.6", "@fluid-experimental/property-dds": "1.3.6", "@fluid-experimental/property-properties": "1.3.6", "@fluid-experimental/property-proxy": "1.3.6", "@fluidframework/azure-client": "1.0.1", "@fluidframework/common-utils": "^3.1.0", "@fluidframework/fluid-static": "1.3.6", "@fluidframework/map": "1.3.6", "@fluidframework/sequence": "1.3.6", "@fluidframework/test-client-utils": "^1.3.7", "@fontsource/open-sans": "^4.5.5", "@math.gl/core": "3.5.7", "@math.gl/culling": "3.5.7", "@microsoft/applicationinsights-react-js": "^3.2.4", "@microsoft/applicationinsights-web": "^2.7.4", "@microsoft/live-share": "^1.0.0-preview.8", "@microsoft/live-share-canvas": "^1.0.0-preview.8", "@microsoft/signalr": "^8.0.0", "@microsoft/teams-js": "^2.18.0", "@mui/icons-material": "^6.1.4", "@mui/lab": "^5.0.0-alpha.102", "@mui/material": "^6.1.4", "@mui/styles": "^5.8.7", "@mui/x-data-grid": "^7.11.0", "@mui/x-data-grid-pro": "^7.29.5", "@mui/x-date-pickers": "^7.11.0", "@mui/x-license": "^7.29.1", "@mui/x-tree-view": "^7.11.0", "@nexusplatform/controllers-additive-manufacturing": "0.1.92", "@nexusplatform/core": "1.1.37", "@nexusplatform/core-react-components": "^0.3.0", "@nexusplatform/react": "^2.0.10", "@nexusplatform/sdc-schema": "1.0.2", "@nexusplatform/sdk-units": "^0.1.114", "@nexusui/branding": "^2.9.0", "@nexusui/components": "^5.3.0", "@nexusui/connected-components": "^4.1.2-alpha.1", "@nexusui/icons": "^3.3.1", "@nexusui/theme": "^5.0.1", "@reduxjs/toolkit": "^1.9.3", "@types/uuid": "^8.3.4", "axios": "^1.8.4", "blueimp-md5": "^2.19.0", "copy-to-clipboard": "^3.3.3", "date-fns": "^2.29.3", "echarts": "^5.4.0", "echarts-for-react": "^3.0.2", "everpolate": "^0.0.3", "history": "^5.3.0", "i18next": "^21.10.0", "i18next-browser-languagedetector": "^7.0.1", "install": "^0.13.0", "launchdarkly-react-client-sdk": "^3.0.3", "material-icons": "^1.10.7", "moment-timezone": "^0.5.37", "msgpackr-extract": "^2.0.2", "npm": "^9.6.7", "oidc-client-ts": "^3.1.0", "qs": "^6.11.0", "querystring": "^0.2.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.27.1", "react-i18next": "^11.18.6", "react-multi-select-component": "^4.2.4", "react-oidc-context": "^3.2.0", "react-redux": "^8.0.5", "react-router-dom": "^6.2.1", "react-select": "^5.2.2", "react-window": "^1.8.2", "redux": "^4.2.1", "redux-persist": "^6.0.0", "tough-cookie": "^4.1.3", "typescript": "^4.5.5", "url": "^0.11.0", "web-vitals": "^2.1.4"}, "scripts": {"start": "npm run copy && cross-env VITE_APP_BASE=dev NODE_OPTIONS=--max_old_space_size=4096 vite", "build": "npm run copy && cross-env GENERATE_SOURCEMAP=false NODE_OPTIONS=--max_old_space_size=4096 VITE_APP_BASE=prod PUBLIC_URL=https://{{HREF_URL}}/platform-landing vite build", "preview": "vite preview", "test": "vitest run", "test:watch": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest run --coverage", "test:ci": "cross-env NODE_ENV=test NODE_OPTIONS=--max_old_space_size=4096 vitest run --coverage", "test:play": "npx playwright test", "lint": "eslint .", "lint-and-fix": "eslint . --fix", "prettier-format": "prettier --config .prettierrc \"src/**/*.{js,ts,tsx}\" --write", "prettier-watch": "onchange 'src/**/*.{js,ts,tsx}' -- prettier --write {{changed}}", "copy": "npm run copyVision && npm run cpWASM && npm run cpWASMT", "copyVision": "npx copyfiles -u 4 ./node_modules/@nexusplatform/visualization/dist/vision/** public/", "cpWASM": "npx copyfiles -u 6 ./node_modules/@nexusplatform/visualization/dist/vision/mMapSDK/WASM/** public/vision/mMapSDK", "cpWASMT": "npx copyfiles -u 6 ./node_modules/@nexusplatform/visualization/dist/vision/mMapSDK/WASMT/** public/vision/mMapSDK", "translate": "nexus-localize translate --key FULL-82CBBEFE-91F9-43EF-8572-A0DEC81741BB"}, "eslintConfig": {"extends": ["react-app"]}, "husky": {"hooks": {"pre-commit": "npm run prettier-format && npm run lint"}}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "overrides": {"react-scripts": {"@svgr/webpack": "^6.5.1", "workbox-webpack-plugin": "7.3.0", "workbox-build": "7.3.0"}}, "devDependencies": {"@babel/preset-env": "^7.16.11", "@babel/preset-react": "^7.16.7", "@babel/preset-typescript": "^7.16.7", "@blakeembrey/template": "^1.2.0", "@emotion/babel-plugin": "^11.9.2", "@emotion/eslint-plugin": "^11.7.0", "@nexusui/scripts": "^0.0.8", "@playwright/test": "^1.22.2", "@rollup/plugin-terser": "^0.4.0", "@svgr/webpack": "^6.5.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^12.1.2", "@testing-library/react-hooks": "^8.0.0", "@testing-library/user-event": "^13.5.0", "@types/node": "^16.11.22", "@types/react": "^17.0.39", "@types/react-dom": "^18.2.0", "@types/react-router-dom": "^5.3.3", "@types/react-window": "^1.8.5", "@types/redux-mock-store": "^1.0.3", "@typescript-eslint/eslint-plugin": "^5.12.1", "@typescript-eslint/parser": "^5.12.1", "@vitejs/plugin-react": "^4.5.0", "@vitest/coverage-v8": "^3.2.0", "allure-playwright": "^2.0.0-beta.15", "buffer": "^6.0.3", "child_process": "^1.0.2", "convert-excel-to-json": "^1.7.0", "cross-env": "^7.0.3", "crypto-browserify": "^3.12.1", "css-minimizer-webpack-plugin": "^7.0.1", "csv": "^6.0.5", "eslint": "^8.13.0", "eslint-config-google": "^0.14.0", "eslint-config-prettier": "^8.4.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-tsdoc": "^0.2.16", "happy-dom": "^17.6.1", "husky": "^7.0.4", "identity-obj-proxy": "^3.0.0", "jsdom": "^26.1.0", "onchange": "^7.1.0", "path-browserify": "^1.0.1", "perf_hooks": "^0.0.1", "prettier": "^2.5.1", "process": "^0.11.10", "react-test-renderer": "^17.0.2", "redux-mock-store": "^1.5.4", "stream-browserify": "^3.0.0", "underscore": "^1.13.2", "util": "^0.12.5", "vite": "^6.3.5", "vite-plugin-environment": "^1.1.3", "vite-plugin-svgr": "^4.3.0", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.2.0", "xlsx": "^0.18.5"}, "optionalDependencies": {"fsevents": "^2.3.2"}}